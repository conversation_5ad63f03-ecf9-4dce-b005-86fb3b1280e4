import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useUserStore } from './user'
import { BASE_URL } from '../config'

export const useWebSocketStore = defineStore('websocket', () => {
  // 状态
  const socket = ref(null)
  const isConnected = ref(false)
  const connectionStatus = ref('disconnected') // disconnected, connecting, connected, error
  const lastMessage = ref(null)
  const heartbeatTimer = ref(null)
  const reconnectTimer = ref(null)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = ref(5)
  
  // 面试状态
  const interviewStatus = ref('idle') // idle, listening, thinking, speaking
  const currentSessionId = ref('')
  
  // 计算属性
  const canSendMessage = computed(() => isConnected.value && socket.value)
  
  // WebSocket URL
  const getWebSocketUrl = () => {
    const userStore = useUserStore()
    // 使用与HTTP API相同的地址和端口，将http替换为ws
    const wsUrl = BASE_URL.replace('http://', 'ws://').replace('https://', 'wss://')
    const baseUrl = `${wsUrl}/ws/interview`
    return `${baseUrl}?token=${userStore.token}`
  }
  
  // 重新连接WebSocket（用于token更新后）
  const reconnectWithNewToken = () => {
    console.log('Token已更新，重新连接WebSocket')
    disconnect()
    // 等待一小段时间确保连接完全关闭
    setTimeout(() => {
      connect()
    }, 100)
  }

  // 连接WebSocket
  const connect = () => {
    if (socket.value && isConnected.value) {
      console.log('WebSocket已连接')
      return
    }

    const userStore = useUserStore()
    if (!userStore.token) {
      console.log('没有token，无法连接WebSocket')
      return
    }

    connectionStatus.value = 'connecting'
    
    try {
      socket.value = uni.connectSocket({
        url: getWebSocketUrl(),
        success: () => {
          console.log('WebSocket连接请求发送成功')
        },
        fail: (error) => {
          console.error('WebSocket连接失败:', error)
          connectionStatus.value = 'error'
          scheduleReconnect()
        }
      })
      
      // 监听连接打开
      socket.value.onOpen(() => {
        console.log('WebSocket连接已打开')
        isConnected.value = true
        connectionStatus.value = 'connected'
        reconnectAttempts.value = 0
        startHeartbeat()
      })
      
      // 监听消息
      socket.value.onMessage((event) => {
        handleMessage(event.data)
      })
      
      // 监听连接关闭
      socket.value.onClose(() => {
        console.log('WebSocket连接已关闭')
        isConnected.value = false
        connectionStatus.value = 'disconnected'
        stopHeartbeat()
        scheduleReconnect()
      })
      
      // 监听错误
      socket.value.onError((error) => {
        console.error('WebSocket错误:', error)
        connectionStatus.value = 'error'
        scheduleReconnect()
      })
      
    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
      connectionStatus.value = 'error'
      scheduleReconnect()
    }
  }
  
  // 断开连接
  const disconnect = () => {
    if (socket.value) {
      socket.value.close()
      socket.value = null
    }
    isConnected.value = false
    connectionStatus.value = 'disconnected'
    stopHeartbeat()
    stopReconnect()
  }
  
  // 发送消息
  const sendMessage = (message) => {
    if (!canSendMessage.value) {
      console.error('WebSocket未连接，无法发送消息')
      return false
    }
    
    try {
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message)
      socket.value.send({
        data: messageStr,
        success: () => {
          console.log('消息发送成功:', messageStr)
        },
        fail: (error) => {
          console.error('消息发送失败:', error)
        }
      })
      return true
    } catch (error) {
      console.error('发送消息异常:', error)
      return false
    }
  }
  
  // 处理接收到的消息
  const handleMessage = (data) => {
    try {
      const message = JSON.parse(data)
      lastMessage.value = message
      
      console.log('收到消息:', message)
      
      switch (message.type) {
        case 'pong':
          // 心跳响应
          break
        case 'status_update':
          interviewStatus.value = message.status
          break
        case 'audio_output':
          // 播放音频
          playAudio(message.data)
          break
        case 'session_start':
          currentSessionId.value = message.session_id
          break
        case 'session_end':
          currentSessionId.value = ''
          interviewStatus.value = 'idle'
          break
        default:
          console.log('未知消息类型:', message.type)
      }
    } catch (error) {
      console.error('解析消息失败:', error)
    }
  }
  
  // 播放音频
  const playAudio = (audioData) => {
    // TODO: 实现音频播放逻辑
    console.log('播放音频:', audioData)
  }
  
  // 发送音频数据
  const sendAudio = (audioData) => {
    return sendMessage({
      type: 'audio_input',
      data: audioData
    })
  }
  
  // 发送打断信号
  const sendInterrupt = () => {
    return sendMessage({
      type: 'interrupt'
    })
  }
  
  // 心跳机制
  const startHeartbeat = () => {
    stopHeartbeat()
    heartbeatTimer.value = setInterval(() => {
      if (canSendMessage.value) {
        sendMessage({ type: 'ping' })
      }
    }, 30000) // 30秒心跳
  }
  
  const stopHeartbeat = () => {
    if (heartbeatTimer.value) {
      clearInterval(heartbeatTimer.value)
      heartbeatTimer.value = null
    }
  }
  
  // 重连机制
  const scheduleReconnect = () => {
    if (reconnectAttempts.value >= maxReconnectAttempts.value) {
      console.log('达到最大重连次数，停止重连')
      return
    }
    
    stopReconnect()
    
    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 30000) // 指数退避，最大30秒
    console.log(`${delay}ms后尝试重连...`)
    
    reconnectTimer.value = setTimeout(() => {
      reconnectAttempts.value++
      connect()
    }, delay)
  }
  
  const stopReconnect = () => {
    if (reconnectTimer.value) {
      clearTimeout(reconnectTimer.value)
      reconnectTimer.value = null
    }
  }
  
  return {
    // 状态
    socket,
    isConnected,
    connectionStatus,
    lastMessage,
    interviewStatus,
    currentSessionId,
    
    // 计算属性
    canSendMessage,
    
    // 动作
    connect,
    disconnect,
    reconnectWithNewToken,
    sendMessage,
    sendAudio,
    sendInterrupt,
    handleMessage
  }
})
