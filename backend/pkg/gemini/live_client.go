package gemini

import (
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"net/url"
	"sync"
	"time"

	"interviewmaster/internal/config"

	"github.com/gorilla/websocket"
)

// LiveClient Gemini Live API客户端
type LiveClient struct {
	apiKey   string
	baseURL  string
	model    string
	sessions map[string]*LiveSession
	mutex    sync.RWMutex
}

// LiveSession Gemini Live会话
type LiveSession struct {
	ID        string                 `json:"id"`
	UserID    uint64                 `json:"user_id"`
	Model     string                 `json:"model"`
	Config    *LiveSessionConfig     `json:"config"`
	Conn      *websocket.Conn        `json:"-"`
	Context   map[string]interface{} `json:"context"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
	IsActive  bool                   `json:"is_active"`
	mutex     sync.RWMutex           `json:"-"`
	ctx       context.Context        `json:"-"`
	cancel    context.CancelFunc     `json:"-"`
}

// LiveSessionConfig Live会话配置
type LiveSessionConfig struct {
	ResponseModalities []string `json:"response_modalities"`
	SystemInstruction  string   `json:"system_instruction"`
	InterviewDomain    string   `json:"interview_domain"`
	PromptVersion      string   `json:"prompt_version"`
}

// LiveMessage Live API消息结构
type LiveMessage struct {
	ClientContent *ClientContent `json:"client_content,omitempty"`
	ServerContent *ServerContent `json:"server_content,omitempty"`
	Data          string         `json:"data,omitempty"`
}

// ClientContent 客户端内容
type ClientContent struct {
	Turns []Turn `json:"turns"`
}

// ServerContent 服务端内容
type ServerContent struct {
	ModelTurn    *ModelTurn `json:"model_turn,omitempty"`
	TurnComplete bool       `json:"turn_complete,omitempty"`
}

// Turn 对话轮次
type Turn struct {
	Role  string `json:"role"`
	Parts []Part `json:"parts"`
}

// ModelTurn 模型轮次
type ModelTurn struct {
	Parts []Part `json:"parts"`
}

// Part 内容部分
type Part struct {
	Text       string      `json:"text,omitempty"`
	InlineData *InlineData `json:"inline_data,omitempty"`
}

// InlineData 内联数据
type InlineData struct {
	MimeType string `json:"mime_type"`
	Data     string `json:"data"`
}

// AudioData 音频数据结构
type AudioData struct {
	Format     string `json:"format"`
	SampleRate int    `json:"sample_rate"`
	Channels   int    `json:"channels"`
	Data       []byte `json:"data"`
}

// Message 消息结构
type Message struct {
	Role      string    `json:"role"`
	Content   []Content `json:"content"`
	Timestamp time.Time `json:"timestamp"`
}

// Content 内容结构
type Content struct {
	Type string      `json:"type"`
	Data interface{} `json:"data"`
}

// NewLiveClient 创建新的Live客户端
func NewLiveClient(cfg *config.GeminiConfig) *LiveClient {
	return &LiveClient{
		apiKey:   cfg.APIKey,
		baseURL:  cfg.BaseURL,
		model:    cfg.Model,
		sessions: make(map[string]*LiveSession),
	}
}

// CreateSession 创建新的Live会话
func (c *LiveClient) CreateSession(userID uint64, domain string, promptVersion string) (*LiveSession, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	sessionID := fmt.Sprintf("session_%d_%d", userID, time.Now().UnixNano())
	log.Printf("创建Gemini Live会话: %s, 用户: %d, 领域: %s", sessionID, userID, domain)

	// 构建系统指令
	systemInstruction := c.buildSystemInstruction(domain, promptVersion)

	// 创建WebSocket连接
	conn, err := c.connectWebSocket()
	if err != nil {
		return nil, fmt.Errorf("连接Gemini Live API失败: %v", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	session := &LiveSession{
		ID:     sessionID,
		UserID: userID,
		Model:  c.model,
		Config: &LiveSessionConfig{
			ResponseModalities: []string{"AUDIO"},
			SystemInstruction:  systemInstruction,
			InterviewDomain:    domain,
			PromptVersion:      promptVersion,
		},
		Conn:      conn,
		Context:   make(map[string]interface{}),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		IsActive:  true,
		ctx:       ctx,
		cancel:    cancel,
	}

	c.sessions[sessionID] = session

	// 发送初始配置
	err = c.sendSetupMessage(session)
	if err != nil {
		c.CloseSession(sessionID)
		return nil, fmt.Errorf("发送初始配置失败: %v", err)
	}

	// 启动消息监听
	go c.listenMessages(session)

	return session, nil
}

// connectWebSocket 连接到Gemini Live API WebSocket
func (c *LiveClient) connectWebSocket() (*websocket.Conn, error) {
	// Gemini Live API WebSocket端点
	wsURL := "wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1alpha.GenerativeService.BidiGenerateContent"

	// 添加API密钥参数
	u, err := url.Parse(wsURL)
	if err != nil {
		return nil, err
	}

	q := u.Query()
	q.Set("key", c.apiKey)
	u.RawQuery = q.Encode()

	log.Printf("连接到Gemini Live API: %s", u.String())

	// 创建WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("WebSocket连接失败: %v", err)
	}

	log.Printf("Gemini Live API WebSocket连接成功")
	return conn, nil
}

// sendSetupMessage 发送初始配置消息
func (c *LiveClient) sendSetupMessage(session *LiveSession) error {
	setupMessage := map[string]interface{}{
		"setup": map[string]interface{}{
			"model": session.Model,
			"generation_config": map[string]interface{}{
				"response_modalities": session.Config.ResponseModalities,
			},
			"system_instruction": map[string]interface{}{
				"parts": []map[string]interface{}{
					{
						"text": session.Config.SystemInstruction,
					},
				},
			},
		},
	}

	return session.Conn.WriteJSON(setupMessage)
}

// buildSystemInstruction 构建系统指令
func (c *LiveClient) buildSystemInstruction(domain string, promptVersion string) string {
	basePrompt := `你是一个专业的面试助手，专门帮助面试者回答技术面试问题。请遵循以下原则：

1. 提供准确、专业的技术回答
2. 回答要简洁明了，重点突出
3. 适当举例说明复杂概念
4. 保持友好和自信的语调
5. 如果不确定答案，诚实说明并提供相关思路

当前面试领域：` + domain

	// 根据A/B测试版本调整提示词
	if promptVersion == "B" {
		basePrompt += `

特别注意：
- 回答要更加详细和深入
- 提供更多实际应用场景
- 包含最佳实践建议`
	}

	return basePrompt
}

// SendAudio 发送音频数据
func (c *LiveClient) SendAudio(sessionID string, audioData *AudioData) (*Message, error) {
	session := c.GetSession(sessionID)
	if session == nil {
		return nil, fmt.Errorf("会话不存在: %s", sessionID)
	}

	session.mutex.Lock()
	defer session.mutex.Unlock()

	log.Printf("发送音频数据到Gemini Live API，会话: %s, 数据大小: %d bytes", sessionID, len(audioData.Data))

	// 将音频数据编码为base64
	audioBase64 := base64.StdEncoding.EncodeToString(audioData.Data)

	// 构建实时输入消息
	realtimeInput := map[string]interface{}{
		"realtime_input": map[string]interface{}{
			"media_chunks": []map[string]interface{}{
				{
					"mime_type": "audio/pcm;rate=16000",
					"data":      audioBase64,
				},
			},
		},
	}

	// 发送消息
	err := session.Conn.WriteJSON(realtimeInput)
	if err != nil {
		return nil, fmt.Errorf("发送音频数据失败: %v", err)
	}

	log.Printf("音频数据发送成功，会话: %s", sessionID)

	// 这里应该等待并返回响应，但为了简化，我们返回一个占位符
	return &Message{
		Role: "assistant",
		Content: []Content{
			{
				Type: "text",
				Data: "音频处理中...",
			},
		},
		Timestamp: time.Now(),
	}, nil
}

// listenMessages 监听来自Gemini Live API的消息
func (c *LiveClient) listenMessages(session *LiveSession) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("监听消息时发生panic: %v", r)
		}
		c.CloseSession(session.ID)
	}()

	for {
		select {
		case <-session.ctx.Done():
			log.Printf("会话 %s 上下文已取消，停止监听", session.ID)
			return
		default:
			var message map[string]interface{}
			err := session.Conn.ReadJSON(&message)
			if err != nil {
				log.Printf("读取Gemini Live API消息失败: %v", err)
				return
			}

			log.Printf("收到Gemini Live API消息: %+v", message)
			c.handleLiveMessage(session, message)
		}
	}
}

// handleLiveMessage 处理来自Gemini Live API的消息
func (c *LiveClient) handleLiveMessage(session *LiveSession, message map[string]interface{}) {
	// 这里需要根据Gemini Live API的响应格式来处理消息
	// 并将音频数据转发给WebSocket客户端
	log.Printf("处理Gemini Live API消息，会话: %s", session.ID)

	// TODO: 实现具体的消息处理逻辑
	// 需要解析音频数据并发送给前端
}

// GetSession 获取会话
func (c *LiveClient) GetSession(sessionID string) *LiveSession {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.sessions[sessionID]
}

// CloseSession 关闭会话
func (c *LiveClient) CloseSession(sessionID string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	session, exists := c.sessions[sessionID]
	if !exists {
		return
	}

	log.Printf("关闭Gemini Live会话: %s", sessionID)

	session.IsActive = false
	if session.cancel != nil {
		session.cancel()
	}
	if session.Conn != nil {
		session.Conn.Close()
	}

	delete(c.sessions, sessionID)
}

// SendText 发送文本消息
func (c *LiveClient) SendText(sessionID string, text string) (*Message, error) {
	session := c.GetSession(sessionID)
	if session == nil {
		return nil, fmt.Errorf("会话不存在: %s", sessionID)
	}

	session.mutex.Lock()
	defer session.mutex.Unlock()

	log.Printf("发送文本消息到Gemini Live API，会话: %s, 文本: %s", sessionID, text)

	// 构建文本输入消息
	textInput := map[string]interface{}{
		"client_content": map[string]interface{}{
			"turns": []map[string]interface{}{
				{
					"role": "user",
					"parts": []map[string]interface{}{
						{
							"text": text,
						},
					},
				},
			},
		},
	}

	// 发送消息
	err := session.Conn.WriteJSON(textInput)
	if err != nil {
		return nil, fmt.Errorf("发送文本消息失败: %v", err)
	}

	log.Printf("文本消息发送成功，会话: %s", sessionID)

	// 返回占位符响应
	return &Message{
		Role: "assistant",
		Content: []Content{
			{
				Type: "text",
				Data: "文本处理中...",
			},
		},
		Timestamp: time.Now(),
	}, nil
}

// GetSessionStats 获取会话统计
func (c *LiveClient) GetSessionStats() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_sessions":  len(c.sessions),
		"active_sessions": 0,
	}

	for _, session := range c.sessions {
		if session.IsActive {
			stats["active_sessions"] = stats["active_sessions"].(int) + 1
		}
	}

	return stats
}
